import { StyleSheet, View, Text, TouchableOpacity, ScrollView } from "react-native";
import React, { useState } from "react";
import HeaderShop from "../../components/shop/HeaderShop";
import NavigateShop from "../../components/shop/NavigateShop";
import { TextField } from 'wini-mobile-components';

// Tab types for affiliate config
enum AffiliateTabType {
    DEFAULT = "DEFAULT",
    SHOP = "SHOP",
    CATEGORY = "CATEGORY"
}

const ConfigAffiliate = () => {
    const [activeTab, setActiveTab] = useState<AffiliateTabType>(AffiliateTabType.DEFAULT);

    // Commission settings state
    const [commissionSettings, setCommissionSettings] = useState({
        customerPurchase: '5',
        f1Reward: '10',
        f2Reward: '5'
    });

    // Tab configuration
    const tabs = [
        {
            id: AffiliateTabType.DEFAULT,
            name: 'Mặc định'
        },
        {
            id: AffiliateTabType.SHOP,
            name: 'SHOP'
        },
        {
            id: AffiliateTabType.CATEGORY,
            name: '<PERSON><PERSON> mục'
        }
    ];

    const renderTabContent = () => {
        switch (activeTab) {
            case AffiliateTabType.DEFAULT:
                return renderDefaultSettings();
            case AffiliateTabType.SHOP:
                return renderShopSettings();
            case AffiliateTabType.CATEGORY:
                return renderCategorySettings();
            default:
                return renderDefaultSettings();
        }
    };

    const handleCommissionChange = (field: string, value: string) => {
        setCommissionSettings(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const renderDefaultSettings = () => (
        <ScrollView style={styles.tabContent}>
            {/* Khách mua */}
            <View style={styles.commissionItem}>
                <Text style={styles.commissionLabel}>Khách mua</Text>
                <View style={styles.percentageContainer}>
                    <Text
                        style={styles.percentageInput}
                    >
                        {commissionSettings.f2Reward}%
                    </Text>
                </View>
            </View>

            {/* Trả thưởng F1 */}
            <View style={styles.commissionItem}>
                <Text style={styles.commissionLabel}>Trả thưởng F1</Text>
                <View style={styles.percentageContainer}>
                    <Text
                        style={styles.percentageInput}
                    >
                        {commissionSettings.f2Reward}%
                    </Text>
                </View>
            </View>

            {/* Trả thưởng F2 */}
            <View style={styles.commissionItem}>
                <Text style={styles.commissionLabel}>Trả thưởng F2</Text>
                <View style={styles.percentageContainer}>
                    <Text
                        style={styles.percentageInput}
                    >
                        {commissionSettings.f2Reward} %
                    </Text>
                </View>
            </View>
        </ScrollView>
    );

    const renderShopSettings = () => (
        <ScrollView style={styles.tabContent}>
            <View style={styles.emptyState}>
                <Text style={styles.emptyText}>Cấu hình SHOP đang được phát triển</Text>
            </View>
        </ScrollView>
    );

    const renderCategorySettings = () => (
        <ScrollView style={styles.tabContent}>
            <View style={styles.emptyState}>
                <Text style={styles.emptyText}>Cấu hình Danh mục đang được phát triển</Text>
            </View>
        </ScrollView>
    );

    return (
        <View style={styles.container}>
            {/* Header */}
            <View style={styles.header}>
                <HeaderShop />
            </View>
            <NavigateShop
                title={'Cấu hình affiliate'}
            />

            {/* Tab Navigation */}
            <View style={styles.tabContainer}>
                {tabs.map((tab) => (
                    <TouchableOpacity
                        key={tab.id}
                        style={styles.tab}
                        onPress={() => setActiveTab(tab.id)}
                    >
                        <Text style={[
                            styles.tabText,
                            activeTab === tab.id && styles.activeTabText
                        ]}>
                            {tab.name}
                        </Text>
                        {activeTab === tab.id && <View style={styles.activeTabIndicator} />}
                    </TouchableOpacity>
                ))}
            </View>

            {/* Tab Content */}
            <View style={styles.content}>
                {renderTabContent()}
            </View>
        </View >
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#fff',
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        maxHeight: 120
    },
    navigator: {
        display: "flex",
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between",
        borderBottomColor: "#00FFFF",
        paddingBottom: 18,
        borderBottomWidth: 0.5,
    },
    content: {
        flex: 1,
        backgroundColor: '#f5f5f5',
    },
    // Tab Navigation Styles
    tabContainer: {
        flexDirection: 'row',
        backgroundColor: '#fff',
        paddingHorizontal: 16,
        paddingTop: 16,
        paddingBottom: 0,
    },
    tab: {
        flex: 1,
        alignItems: 'center',
        paddingVertical: 12,
        paddingBottom: 16,
        position: 'relative',
    },
    tabText: {
        fontSize: 14,
        color: '#999',
        fontWeight: '400',
    },
    activeTabText: {
        color: '#00BFFF',
        fontWeight: '600',
    },
    activeTabIndicator: {
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        height: 2,
        backgroundColor: '#00BFFF',
    },
    // Content Styles
    tabContent: {
        flex: 1,
        padding: 16,
        backgroundColor: '#fff',
    },
    // Commission Item Styles
    commissionItem: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        backgroundColor: '#fff',
        marginVertical: 10, 
    },
    commissionLabel: {
        fontSize: 14,
        color: '#333',
        fontWeight: '400',
        flex: 1,
    },
    percentageContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#f0f0f0',
        borderRadius: 6,        
        paddingVertical: 10,    
        
    },
    percentageInput: {
        fontSize: 11,
        color: '#333',
        fontWeight: '600',
        textAlign: 'left',
        minWidth: 200,
        borderWidth: 0,
        padding: 16,
    },
    // Empty State
    emptyState: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingVertical: 60,
    },
    emptyText: {
        fontSize: 16,
        color: '#999',
        textAlign: 'center',
    },

});

export default ConfigAffiliate


