import { StyleSheet, View, Text, TouchableOpacity, ScrollView } from "react-native";
import React, { useState } from "react";
import HeaderShop from "../../components/shop/HeaderShop";
import NavigateShop from "../../components/shop/NavigateShop";
import { TextField } from 'wini-mobile-components';

// Tab types for affiliate config
enum AffiliateTabType {
    DEFAULT = "DEFAULT",
    SHOP = "SHOP",
    CATEGORY = "CATEGORY"
}

const ConfigAffiliate = () => {
    const [activeTab, setActiveTab] = useState<AffiliateTabType>(AffiliateTabType.DEFAULT);
    const [shopSettings, setShopSettings] = useState({
        customerPurchase: '5',
        f1Reward: '10',
        f2Reward: '5',
        isEnabled: true
    });

    // Edit mode state for shop settings
    const [isEditMode, setIsEditMode] = useState(false);
    const [tempShopSettings, setTempShopSettings] = useState({
        customerPurchase: '5',
        f1Reward: '10',
        f2Reward: '5'
    });

    const handleShopSettingsChange = (field: string, value: string | boolean) => {
        setShopSettings(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const handleTempSettingsChange = (field: string, value: string) => {
        setTempShopSettings(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const handleEditPress = () => {
        setTempShopSettings({
            customerPurchase: shopSettings.customerPurchase,
            f1Reward: shopSettings.f1Reward,
            f2Reward: shopSettings.f2Reward
        });
        setIsEditMode(true);
    };

    const handleSavePress = () => {
        setShopSettings(prev => ({
            ...prev,
            ...tempShopSettings
        }));
        setIsEditMode(false);
    };

    const handleCancelPress = () => {
        setIsEditMode(false);
        setTempShopSettings({
            customerPurchase: shopSettings.customerPurchase,
            f1Reward: shopSettings.f1Reward,
            f2Reward: shopSettings.f2Reward
        });
    };

    // Commission settings state
    const [commissionSettings, setCommissionSettings] = useState({
        customerPurchase: '5',
        f1Reward: '10',
        f2Reward: '5'
    });

    // Tab configuration
    const tabs = [
        {
            id: AffiliateTabType.DEFAULT,
            name: 'Mặc định'
        },
        {
            id: AffiliateTabType.SHOP,
            name: 'SHOP'
        },
        {
            id: AffiliateTabType.CATEGORY,
            name: 'Danh mục'
        }
    ];

    const renderTabContent = () => {
        switch (activeTab) {
            case AffiliateTabType.DEFAULT:
                return renderDefaultSettings();
            case AffiliateTabType.SHOP:
                return renderShopSettings();
            case AffiliateTabType.CATEGORY:
                return renderCategorySettings();
            default:
                return renderDefaultSettings();
        }
    };

    const handleCommissionChange = (field: string, value: string) => {
        setCommissionSettings(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const renderDefaultSettings = () => (
        <ScrollView style={styles.tabContent}>
            {/* Khách mua */}
            <View style={styles.commissionItem}>
                <Text style={styles.commissionLabel}>Khách mua</Text>
                <View style={styles.percentageContainer}>
                    <Text
                        style={styles.percentageInput}
                    >
                        {commissionSettings.f2Reward}%
                    </Text>
                </View>
            </View>

            {/* Trả thưởng F1 */}
            <View style={styles.commissionItem}>
                <Text style={styles.commissionLabel}>Trả thưởng F1</Text>
                <View style={styles.percentageContainer}>
                    <Text
                        style={styles.percentageInput}
                    >
                        {commissionSettings.f2Reward}%
                    </Text>
                </View>
            </View>

            {/* Trả thưởng F2 */}
            <View style={styles.commissionItem}>
                <Text style={styles.commissionLabel}>Trả thưởng F2</Text>
                <View style={styles.percentageContainer}>
                    <Text
                        style={styles.percentageInput}
                    >
                        {commissionSettings.f2Reward} %
                    </Text>
                </View>
            </View>
        </ScrollView>
    );

    const renderShopSettings = () => (
        <ScrollView style={styles.tabContent}>
            {/* Edit/Save/Cancel Buttons */}
            <View style={styles.editButtonContainer}>
                {!isEditMode ? (
                    <TouchableOpacity style={styles.editButton} onPress={handleEditPress}>
                        <Text style={styles.editButtonText}>Edit</Text>
                    </TouchableOpacity>
                ) : (
                    <View style={styles.actionButtonsContainer}>
                        <TouchableOpacity style={styles.saveButton} onPress={handleSavePress}>
                            <Text style={styles.saveButtonText}>✓</Text>
                        </TouchableOpacity>
                        <TouchableOpacity style={styles.cancelButton} onPress={handleCancelPress}>
                            <Text style={styles.cancelButtonText}>✕</Text>
                        </TouchableOpacity>
                    </View>
                )}
            </View>

            {/* Khách mua */}
            <View style={styles.commissionItem}>
                <Text style={styles.commissionLabel}>Khách mua</Text>
                <View style={styles.percentageContainer}>
                    {isEditMode ? (
                        <TextField
                            value={tempShopSettings.customerPurchase}
                            onChange={(value: string) => handleTempSettingsChange('customerPurchase', value)}
                            style={styles.percentageInputEdit}
                        />
                    ) : (
                        <Text style={styles.percentageInput}>
                            {shopSettings.customerPurchase}%
                        </Text>
                    )}
                </View>
            </View>

            {/* Trả thưởng F1 */}
            <View style={styles.commissionItem}>
                <Text style={styles.commissionLabel}>Trả thưởng F1</Text>
                <View style={styles.percentageContainer}>
                    {isEditMode ? (
                        <TextField
                            value={tempShopSettings.f1Reward}
                            onChange={(value: string) => handleTempSettingsChange('f1Reward', value)}
                            style={styles.percentageInputEdit}
                        />
                    ) : (
                        <Text style={styles.percentageInput}>
                            {shopSettings.f1Reward}%
                        </Text>
                    )}
                </View>
            </View>

            {/* Trả thưởng F2 */}
            <View style={styles.commissionItem}>
                <Text style={styles.commissionLabel}>Trả thưởng F2</Text>
                <View style={styles.percentageContainer}>
                    {isEditMode ? (
                        <TextField
                            value={tempShopSettings.f2Reward}
                            onChange={(value: string) => handleTempSettingsChange('f2Reward', value)}
                            style={styles.percentageInputEdit}
                        />
                    ) : (
                        <Text style={styles.percentageInput}>
                            {shopSettings.f2Reward}%
                        </Text>
                    )}
                </View>
            </View>

            {/* Toggle Switch */}
            <View style={styles.toggleContainer}>
                <Text style={styles.toggleLabel}>ON</Text>
                <TouchableOpacity
                    style={[styles.toggleSwitch, shopSettings.isEnabled && styles.toggleSwitchActive]}
                    onPress={() => handleShopSettingsChange('isEnabled', !shopSettings.isEnabled)}
                >
                    <View style={[
                        styles.toggleThumb,
                        shopSettings.isEnabled && styles.toggleThumbActive
                    ]} />
                </TouchableOpacity>
            </View>
        </ScrollView>
    );

    const renderCategorySettings = () => (
        <ScrollView style={styles.tabContent}>
            <View style={styles.emptyState}>
                <Text style={styles.emptyText}>Cấu hình Danh mục đang được phát triển</Text>
            </View>
        </ScrollView>
    );

    return (
        <View style={styles.container}>
            {/* Header */}
            <View style={styles.header}>
                <HeaderShop />
            </View>
            <NavigateShop
                title={'Cấu hình affiliate'}
            />

            {/* Tab Navigation */}
            <View style={styles.tabContainer}>
                {tabs.map((tab) => (
                    <TouchableOpacity
                        key={tab.id}
                        style={styles.tab}
                        onPress={() => setActiveTab(tab.id)}
                    >
                        <Text style={[
                            styles.tabText,
                            activeTab === tab.id && styles.activeTabText
                        ]}>
                            {tab.name}
                        </Text>
                        {activeTab === tab.id && <View style={styles.activeTabIndicator} />}
                    </TouchableOpacity>
                ))}
            </View>

            {/* Tab Content */}
            <View style={styles.content}>
                {renderTabContent()}
            </View>
        </View >
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#fff',
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        maxHeight: 120
    },
    navigator: {
        display: "flex",
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between",
        borderBottomColor: "#00FFFF",
        paddingBottom: 18,
        borderBottomWidth: 0.5,
    },
    content: {
        flex: 1,
        backgroundColor: '#f5f5f5',
    },
    // Tab Navigation Styles
    tabContainer: {
        flexDirection: 'row',
        backgroundColor: '#fff',
        paddingHorizontal: 16,
        paddingTop: 16,
        paddingBottom: 0,
    },
    tab: {
        flex: 1,
        alignItems: 'center',
        paddingVertical: 12,
        paddingBottom: 16,
        position: 'relative',
    },
    tabText: {
        fontSize: 14,
        color: '#999',
        fontWeight: '400',
    },
    activeTabText: {
        color: '#00BFFF',
        fontWeight: '600',
    },
    activeTabIndicator: {
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        height: 2,
        backgroundColor: '#00BFFF',
    },
    // Content Styles
    tabContent: {
        flex: 1,
        padding: 16,
        backgroundColor: '#fff',
    },
    // Commission Item Styles
    commissionItem: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        backgroundColor: '#fff',
        marginVertical: 10, 
    },
    commissionLabel: {
        fontSize: 14,
        color: '#333',
        fontWeight: '400',
        flex: 1,
    },
    percentageContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#f0f0f0',
        borderRadius: 6,        
        paddingVertical: 10,    
        
    },
    percentageInput: {
        fontSize: 11,
        color: '#333',
        fontWeight: '600',
        textAlign: 'left',
        minWidth: 200,
        borderWidth: 0,
        padding: 16,
    },
    // Empty State
    emptyState: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingVertical: 60,
    },
    emptyText: {
        fontSize: 16,
        color: '#999',
        textAlign: 'center',
    },
    // Toggle Switch Styles
    toggleContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-start',
        backgroundColor: '#fff',
        marginVertical: 10,
    },
    toggleLabel: {
        fontSize: 14,
        marginRight: 10,
        color: '#333',
        fontWeight: '400',
    },
    toggleSwitch: {
        width: 50,
        height: 30,
        borderRadius: 15,
        backgroundColor: '#ccc',
        justifyContent: 'center',
        alignItems: 'center',
        padding: 5,
    },
    toggleSwitchActive: {
        backgroundColor: '#1C33FF',
    },
    toggleThumb: {
        width: 20,
        height: 20,
        borderRadius: 10,
        backgroundColor: '#fff',
    },
    toggleThumbActive: {
        transform: [{ translateX: 12 }],
    },
    // Edit Button Styles
    editButtonContainer: {
        alignItems: 'flex-end',
        marginBottom: 16,
    },
    editButton: {
        backgroundColor: '#007AFF',
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderRadius: 6,
    },
    editButtonText: {
        color: '#fff',
        fontSize: 14,
        fontWeight: '600',
    },
    actionButtonsContainer: {
        flexDirection: 'row',
        gap: 8,
    },
    saveButton: {
        backgroundColor: '#4CAF50',
        paddingHorizontal: 12,
        paddingVertical: 8,
        borderRadius: 6,
        minWidth: 40,
        alignItems: 'center',
    },
    saveButtonText: {
        color: '#fff',
        fontSize: 16,
        fontWeight: '600',
    },
    cancelButton: {
        backgroundColor: '#F44336',
        paddingHorizontal: 12,
        paddingVertical: 8,
        borderRadius: 6,
        minWidth: 40,
        alignItems: 'center',
    },
    cancelButtonText: {
        color: '#fff',
        fontSize: 16,
        fontWeight: '600',
    },
    percentageInputEdit: {
        fontSize: 16,
        color: '#333',
        fontWeight: '600',
        textAlign: 'center',
        minWidth: 30,
        backgroundColor: '#fff',
        borderWidth: 1,
        borderColor: '#007AFF',
        borderRadius: 4,
        padding: 8,
    },

});

export default ConfigAffiliate


